#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试计算器控制功能
"""

import subprocess
import time


def test_calculator_control():
    """快速测试计算器打开和关闭"""
    print("🧪 快速测试计算器控制功能")
    print("=" * 50)
    
    # 1. 打开计算器
    print("\n1️⃣ 测试打开计算器...")
    try:
        # 使用最可能成功的命令
        process = subprocess.Popen(['start', 'ms-calculator:'], shell=True)
        time.sleep(2)
        print("✅ 计算器打开命令已执行")
        
        # 用户确认
        user_input = input("❓ 您是否看到计算器窗口？(y/n): ").lower().strip()
        if user_input != 'y':
            print("❌ 计算器打开失败")
            return False
        
        print("✅ 计算器打开成功")
        
    except Exception as e:
        print(f"❌ 打开计算器失败: {e}")
        return False
    
    # 2. 关闭计算器
    print("\n2️⃣ 测试关闭计算器...")
    
    # 尝试多种关闭方法
    close_commands = [
        ['wmic', 'process', 'where', 'name="Calculator.exe"', 'delete'],
        ['wmic', 'process', 'where', 'name="WindowsCalculator.exe"', 'delete'],
        ['powershell', '-Command', '& {Get-Process -Name "*calculator*" -ErrorAction SilentlyContinue | Stop-Process -Force}'],
        ['powershell', '-Command', '& {Get-Process -Name "Calculator" -ErrorAction SilentlyContinue | Stop-Process -Force}'],
        ['taskkill', '/F', '/IM', 'Calculator.exe'],
        ['taskkill', '/F', '/IM', 'WindowsCalculator.exe'],
        ['taskkill', '/F', '/FI', 'IMAGENAME eq Calculator.exe'],
    ]
    
    success = False
    for i, cmd in enumerate(close_commands, 1):
        print(f"   尝试方法 {i}: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"   ✅ 命令执行成功")
                time.sleep(1)
                
                # 用户确认
                user_input = input("   ❓ 计算器是否已关闭？(y/n): ").lower().strip()
                if user_input == 'y':
                    print("   ✅ 计算器关闭成功")
                    success = True
                    break
                else:
                    print("   ❌ 计算器仍然打开")
            else:
                print(f"   ❌ 命令执行失败: {result.stderr}")
        except Exception as e:
            print(f"   ❌ 命令执行异常: {e}")
    
    if success:
        print("\n🎉 计算器控制功能测试通过！")
        return True
    else:
        print("\n❌ 计算器关闭功能测试失败")
        return False


def main():
    """主函数"""
    print("快速测试工具 - 计算器控制功能")
    print("这个工具将快速测试计算器的打开和关闭功能")
    
    input("\n按回车键开始测试...")
    
    if test_calculator_control():
        print("\n✅ 测试结果：功能正常")
        print("您的手势控制计算器程序应该可以正常工作了！")
    else:
        print("\n❌ 测试结果：功能异常")
        print("建议运行 debug_calculator_control.py 进行详细诊断")
    
    input("\n按回车键退出...")


if __name__ == "__main__":
    main()
