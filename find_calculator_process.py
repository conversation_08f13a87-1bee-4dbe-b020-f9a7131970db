#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找计算器进程的实际名称
"""

import subprocess
import time


def find_calculator_processes():
    """查找所有可能的计算器进程"""
    print("🔍 查找计算器进程...")
    print("=" * 50)
    
    # 首先打开计算器
    print("1. 打开计算器...")
    try:
        subprocess.Popen(['start', 'ms-calculator:'], shell=True)
        time.sleep(3)  # 等待计算器启动
        print("✅ 计算器启动命令已执行")
    except Exception as e:
        print(f"❌ 启动计算器失败: {e}")
        return
    
    input("请确认计算器已打开，然后按回车键继续...")
    
    # 查找所有进程
    print("\n2. 查找所有进程...")
    
    # 方法1: 使用tasklist查找所有进程，然后筛选
    print("\n📋 方法1: 使用tasklist查找包含'calc'的进程")
    try:
        result = subprocess.run(['tasklist'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            calc_processes = []
            for line in lines:
                if 'calc' in line.lower() or 'calculator' in line.lower():
                    calc_processes.append(line.strip())
            
            if calc_processes:
                print("✅ 找到以下相关进程:")
                for process in calc_processes:
                    print(f"   {process}")
            else:
                print("❌ 未找到包含'calc'或'calculator'的进程")
        else:
            print(f"❌ tasklist执行失败: {result.stderr}")
    except Exception as e:
        print(f"❌ tasklist查询失败: {e}")
    
    # 方法2: 使用PowerShell查找
    print("\n📋 方法2: 使用PowerShell查找所有进程")
    try:
        # 修复的PowerShell命令
        cmd = ['powershell', '-Command', 'Get-Process | Where-Object {$_.ProcessName -like "*calc*" -or $_.ProcessName -like "*Calculator*"} | Select-Object ProcessName, Id, MainWindowTitle']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0 and result.stdout.strip():
            print("✅ PowerShell找到以下进程:")
            print(result.stdout)
        else:
            print("❌ PowerShell未找到相关进程")
            if result.stderr:
                print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"❌ PowerShell查询失败: {e}")
    
    # 方法3: 查找UWP应用
    print("\n📋 方法3: 查找UWP应用进程")
    try:
        cmd = ['powershell', '-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -like "*计算器*" -or $_.MainWindowTitle -like "*Calculator*"} | Select-Object ProcessName, Id, MainWindowTitle']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0 and result.stdout.strip():
            print("✅ 找到有窗口标题的计算器进程:")
            print(result.stdout)
        else:
            print("❌ 未找到有计算器窗口标题的进程")
    except Exception as e:
        print(f"❌ UWP应用查询失败: {e}")
    
    # 方法4: 查找所有Microsoft应用
    print("\n📋 方法4: 查找Microsoft相关进程")
    try:
        cmd = ['powershell', '-Command', 'Get-Process | Where-Object {$_.ProcessName -like "*Microsoft*" -or $_.Company -like "*Microsoft*"} | Where-Object {$_.MainWindowTitle -ne ""} | Select-Object ProcessName, Id, MainWindowTitle']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        if result.returncode == 0 and result.stdout.strip():
            print("✅ Microsoft相关进程:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'calc' in line.lower() or 'calculator' in line.lower() or '计算器' in line:
                    print(f"   {line}")
        else:
            print("❌ 未找到Microsoft相关进程")
    except Exception as e:
        print(f"❌ Microsoft进程查询失败: {e}")


def test_close_methods():
    """测试不同的关闭方法"""
    print("\n" + "=" * 50)
    print("🧪 测试关闭方法")
    print("=" * 50)
    
    # 简化的PowerShell命令
    close_commands = [
        # 简化的PowerShell语法
        ['powershell', '-Command', 'Get-Process -Name "*calc*" | Stop-Process -Force'],
        ['powershell', '-Command', 'Get-Process | Where-Object {$_.ProcessName -like "*calc*"} | Stop-Process -Force'],
        ['powershell', '-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -like "*计算器*"} | Stop-Process -Force'],
        ['powershell', '-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -like "*Calculator*"} | Stop-Process -Force'],
        # 使用wmic（如果可用）
        ['powershell', '-Command', 'Get-WmiObject -Class Win32_Process | Where-Object {$_.Name -like "*calc*"} | ForEach-Object {$_.Terminate()}'],
    ]
    
    for i, cmd in enumerate(close_commands, 1):
        print(f"\n{i}. 测试命令: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            print(f"   返回码: {result.returncode}")
            if result.stdout:
                print(f"   输出: {result.stdout.strip()}")
            if result.stderr:
                print(f"   错误: {result.stderr.strip()}")
            
            if result.returncode == 0:
                print("   ✅ 命令执行成功")
                user_input = input("   ❓ 计算器是否已关闭？(y/n): ").lower().strip()
                if user_input == 'y':
                    print("   🎉 找到有效的关闭方法！")
                    return cmd
            else:
                print("   ❌ 命令执行失败")
                
        except subprocess.TimeoutExpired:
            print("   ⏰ 命令超时")
        except Exception as e:
            print(f"   ❌ 命令异常: {e}")
    
    print("\n❌ 所有测试方法都失败了")
    return None


def main():
    """主函数"""
    print("计算器进程查找工具")
    print("这个工具将帮助找到计算器的实际进程名称")
    
    # 查找进程
    find_calculator_processes()
    
    # 测试关闭方法
    effective_method = test_close_methods()
    
    if effective_method:
        print(f"\n✅ 有效的关闭命令: {' '.join(effective_method)}")
        print("请将此命令添加到主程序中")
    else:
        print("\n❌ 未找到有效的关闭方法")
        print("建议手动关闭计算器并检查系统设置")


if __name__ == "__main__":
    main()
