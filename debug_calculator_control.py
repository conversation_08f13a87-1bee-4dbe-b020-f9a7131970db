#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算器控制调试工具
用于测试和调试计算器的打开和关闭功能
"""

import subprocess
import time
import sys


class DebugCalculatorController:
    """调试版计算器控制类"""
    
    def __init__(self):
        self.calculator_process = None
        self.is_calculator_open = False
        self.calculator_type = None
        self.last_command = None
        
    def test_open_calculator(self):
        """测试打开计算器的所有方法"""
        print("=" * 60)
        print("测试计算器打开功能")
        print("=" * 60)
        
        calculator_commands = [
            # Windows 10/11 UWP计算器
            ['start', 'ms-calculator:'],
            # 传统计算器
            ['calc.exe'],
            ['calculator.exe'],
            ['C:\\Windows\\System32\\calc.exe'],
            # PowerShell方式启动UWP应用
            ['powershell', '-Command', 'Start-Process', 'calculator:']
        ]
        
        for i, cmd in enumerate(calculator_commands, 1):
            print(f"\n{i}. 测试命令: {' '.join(cmd)}")
            try:
                if cmd[0] == 'start':
                    process = subprocess.Popen(cmd, shell=True)
                    calc_type = 'UWP'
                elif cmd[0] == 'powershell':
                    process = subprocess.Popen(cmd, shell=True)
                    calc_type = 'UWP'
                else:
                    process = subprocess.Popen(cmd)
                    calc_type = 'Win32'
                
                time.sleep(2)
                
                if calc_type == 'UWP' or process.poll() is None:
                    print(f"   ✅ 成功启动 (类型: {calc_type})")
                    
                    # 询问用户是否看到计算器
                    user_input = input("   ❓ 您是否看到计算器窗口？(y/n): ").lower().strip()
                    if user_input == 'y':
                        print("   ✅ 用户确认计算器已打开")
                        self.calculator_process = process
                        self.is_calculator_open = True
                        self.calculator_type = calc_type
                        self.last_command = cmd
                        return True
                    else:
                        print("   ❌ 用户未看到计算器窗口")
                        if process.poll() is None:
                            try:
                                process.terminate()
                            except:
                                pass
                else:
                    print(f"   ❌ 进程已退出 (返回码: {process.returncode})")
                    
            except FileNotFoundError:
                print(f"   ❌ 命令未找到")
            except Exception as e:
                print(f"   ❌ 执行失败: {e}")
        
        print("\n❌ 所有打开方法都失败了")
        return False
    
    def test_close_calculator(self):
        """测试关闭计算器的所有方法"""
        print("\n" + "=" * 60)
        print("测试计算器关闭功能")
        print("=" * 60)
        
        if not self.is_calculator_open:
            print("❌ 计算器未打开，请先运行打开测试")
            return False
        
        print(f"当前计算器类型: {self.calculator_type}")
        print(f"启动命令: {' '.join(self.last_command) if self.last_command else 'Unknown'}")
        
        # 测试不同的关闭方法
        close_methods = [
            ("PowerShell - Stop-Process Calculator", 
             ['powershell', '-Command', 'Stop-Process', '-Name', 'Calculator', '-Force']),
            ("PowerShell - Get-Process calculator", 
             ['powershell', '-Command', 'Get-Process', '*calculator*', '|', 'Stop-Process', '-Force']),
            ("taskkill Calculator.exe", 
             ['taskkill', '/F', '/IM', 'Calculator.exe']),
            ("taskkill calc.exe", 
             ['taskkill', '/F', '/IM', 'calc.exe']),
            ("taskkill WindowsCalculator", 
             ['taskkill', '/F', '/IM', 'WindowsCalculator.exe']),
        ]
        
        for i, (method_name, cmd) in enumerate(close_methods, 1):
            print(f"\n{i}. 测试方法: {method_name}")
            print(f"   命令: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                print(f"   返回码: {result.returncode}")
                
                if result.stdout:
                    print(f"   输出: {result.stdout.strip()}")
                if result.stderr:
                    print(f"   错误: {result.stderr.strip()}")
                
                if result.returncode == 0:
                    print("   ✅ 命令执行成功")
                    
                    # 询问用户计算器是否关闭
                    user_input = input("   ❓ 计算器是否已关闭？(y/n): ").lower().strip()
                    if user_input == 'y':
                        print("   ✅ 用户确认计算器已关闭")
                        self.is_calculator_open = False
                        self.calculator_process = None
                        self.calculator_type = None
                        self.last_command = None
                        return True
                    else:
                        print("   ❌ 用户确认计算器仍然打开")
                else:
                    print(f"   ❌ 命令执行失败")
                    
            except subprocess.TimeoutExpired:
                print("   ❌ 命令执行超时")
            except Exception as e:
                print(f"   ❌ 命令执行异常: {e}")
        
        # 如果是Win32类型，尝试直接终止进程
        if self.calculator_type == 'Win32' and self.calculator_process:
            print(f"\n{len(close_methods) + 1}. 测试方法: 直接终止进程")
            try:
                self.calculator_process.terminate()
                self.calculator_process.wait(timeout=3)
                print("   ✅ 进程终止成功")
                
                user_input = input("   ❓ 计算器是否已关闭？(y/n): ").lower().strip()
                if user_input == 'y':
                    print("   ✅ 用户确认计算器已关闭")
                    self.is_calculator_open = False
                    self.calculator_process = None
                    self.calculator_type = None
                    self.last_command = None
                    return True
                    
            except Exception as e:
                print(f"   ❌ 进程终止失败: {e}")
        
        print("\n❌ 所有关闭方法都失败了")
        return False
    
    def list_calculator_processes(self):
        """列出所有计算器相关进程"""
        print("\n" + "=" * 60)
        print("查找计算器相关进程")
        print("=" * 60)
        
        commands = [
            ("tasklist 查找 Calculator", ['tasklist', '/FI', 'IMAGENAME eq Calculator.exe']),
            ("tasklist 查找 calc", ['tasklist', '/FI', 'IMAGENAME eq calc.exe']),
            ("PowerShell 查找 calculator", ['powershell', '-Command', 'Get-Process', '*calculator*']),
            ("PowerShell 查找所有进程包含calc", ['powershell', '-Command', 'Get-Process', '*calc*']),
        ]
        
        for method_name, cmd in commands:
            print(f"\n{method_name}:")
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and result.stdout.strip():
                    print(f"✅ 找到进程:")
                    print(result.stdout)
                else:
                    print("❌ 未找到相关进程")
                    if result.stderr:
                        print(f"错误: {result.stderr}")
            except Exception as e:
                print(f"❌ 查询失败: {e}")


def main():
    """主函数"""
    print("计算器控制调试工具")
    print("这个工具将帮助您诊断计算器打开和关闭的问题")
    print("\n请确保：")
    print("1. 当前没有打开的计算器窗口")
    print("2. 您有足够的权限执行系统命令")
    print("3. 您可以看到屏幕上的计算器窗口")
    
    input("\n按回车键开始测试...")
    
    controller = DebugCalculatorController()
    
    # 首先列出现有进程
    controller.list_calculator_processes()
    
    # 测试打开
    if controller.test_open_calculator():
        print("\n✅ 计算器打开测试成功")
        
        # 测试关闭
        if controller.test_close_calculator():
            print("\n✅ 计算器关闭测试成功")
            print("\n🎉 所有测试通过！您的系统支持计算器控制功能。")
        else:
            print("\n❌ 计算器关闭测试失败")
            print("\n💡 建议：")
            print("1. 检查是否有足够的权限关闭应用程序")
            print("2. 尝试手动关闭计算器，然后重新测试")
            print("3. 考虑使用管理员权限运行程序")
    else:
        print("\n❌ 计算器打开测试失败")
        print("\n💡 建议：")
        print("1. 检查Windows版本和计算器应用是否正常")
        print("2. 尝试手动打开计算器确认功能正常")
        print("3. 检查系统权限设置")
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)


if __name__ == "__main__":
    main()
