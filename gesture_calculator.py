#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时手势控制计算器应用程序
使用MediaPipe Hands进行手势识别，Qt5构建界面
"""

import sys
import cv2
import numpy as np
import mediapipe as mp
import subprocess
import os
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                             QHBoxLayout, QWidget, QLabel, QPushButton)
from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt
from PyQt5.QtGui import QImage, QPixmap, QFont


class GestureRecognizer:
    """手势识别类"""
    
    def __init__(self):
        self.mp_hands = mp.solutions.hands
        self.hands = self.mp_hands.Hands(
            static_image_mode=False,
            max_num_hands=1,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        # 手势状态
        self.current_gesture = "未检测到"
        self.gesture_confidence = 0.0
        
        # 手势稳定性检测
        self.gesture_history = []
        self.history_size = 10
        self.stability_threshold = 0.7
        
    def detect_gesture(self, landmarks):
        """检测手势类型"""
        if not landmarks:
            return "未检测到", 0.0
            
        # 获取关键点坐标
        points = []
        for lm in landmarks.landmark:
            points.append([lm.x, lm.y])
        points = np.array(points)
        
        # 检测握拳手势
        fist_score = self._detect_fist(points)
        
        # 检测张开手掌手势
        open_palm_score = self._detect_open_palm(points)
        
        # 判断手势
        if fist_score > 0.8:
            gesture = "握拳"
            confidence = fist_score
        elif open_palm_score > 0.8:
            gesture = "张开手掌"
            confidence = open_palm_score
        else:
            gesture = "其他"
            confidence = max(fist_score, open_palm_score)
            
        # 添加到历史记录
        self.gesture_history.append(gesture)
        if len(self.gesture_history) > self.history_size:
            self.gesture_history.pop(0)
            
        # 检查手势稳定性
        if len(self.gesture_history) >= self.history_size:
            stable_gesture = self._get_stable_gesture()
            if stable_gesture:
                return stable_gesture, confidence
                
        return gesture, confidence
    
    def _detect_fist(self, points):
        """检测握拳手势"""
        # 手指尖点索引：拇指4, 食指8, 中指12, 无名指16, 小指20
        # 手指根部点索引：拇指3, 食指6, 中指10, 无名指14, 小指18
        
        finger_tips = [4, 8, 12, 16, 20]
        finger_bases = [3, 6, 10, 14, 18]
        
        # 计算手指弯曲程度
        curl_scores = []
        
        for i in range(5):
            tip = points[finger_tips[i]]
            base = points[finger_bases[i]]
            
            # 计算手指长度和弯曲程度
            if i == 0:  # 拇指特殊处理
                # 拇指与手掌中心的距离
                palm_center = points[9]  # 中指根部作为手掌中心
                tip_distance = np.linalg.norm(tip - palm_center)
                base_distance = np.linalg.norm(base - palm_center)
                curl_score = 1 - (tip_distance / (base_distance + 0.001))
            else:
                # 其他手指：检查指尖是否低于指根
                curl_score = 1 if tip[1] > base[1] else 0
                
            curl_scores.append(max(0, curl_score))
        
        # 握拳得分：所有手指都弯曲
        fist_score = np.mean(curl_scores)
        return fist_score
    
    def _detect_open_palm(self, points):
        """检测张开手掌手势"""
        finger_tips = [4, 8, 12, 16, 20]
        finger_bases = [3, 6, 10, 14, 18]
        
        # 计算手指伸展程度
        extend_scores = []
        
        for i in range(5):
            tip = points[finger_tips[i]]
            base = points[finger_bases[i]]
            
            if i == 0:  # 拇指特殊处理
                palm_center = points[9]
                tip_distance = np.linalg.norm(tip - palm_center)
                base_distance = np.linalg.norm(base - palm_center)
                extend_score = tip_distance / (base_distance + 0.001) - 1
                extend_score = max(0, min(1, extend_score))
            else:
                # 其他手指：检查指尖是否高于指根
                extend_score = 1 if tip[1] < base[1] else 0
                
            extend_scores.append(extend_score)
        
        # 张开手掌得分：所有手指都伸展
        open_palm_score = np.mean(extend_scores)
        return open_palm_score
    
    def _get_stable_gesture(self):
        """获取稳定的手势"""
        if len(self.gesture_history) < self.history_size:
            return None
            
        # 统计最近的手势
        gesture_counts = {}
        for gesture in self.gesture_history:
            gesture_counts[gesture] = gesture_counts.get(gesture, 0) + 1
            
        # 找到最频繁的手势
        max_count = max(gesture_counts.values())
        if max_count / len(self.gesture_history) >= self.stability_threshold:
            for gesture, count in gesture_counts.items():
                if count == max_count:
                    return gesture
        return None


class CalculatorController:
    """计算器控制类"""

    def __init__(self):
        self.calculator_process = None
        self.is_calculator_open = False
        self.calculator_type = None  # 记录使用的计算器类型
        self.last_command = None     # 记录最后使用的命令
        
    def open_calculator(self):
        """打开系统计算器"""
        if not self.is_calculator_open:
            try:
                # 尝试多种方式打开Windows计算器
                calculator_commands = [
                    # Windows 10/11 UWP计算器
                    ['start', 'ms-calculator:'],
                    # 传统计算器
                    ['calc.exe'],
                    ['calculator.exe'],
                    ['C:\\Windows\\System32\\calc.exe'],
                    # PowerShell方式启动UWP应用
                    ['powershell', '-Command', 'Start-Process', 'calculator:']
                ]

                for cmd in calculator_commands:
                    try:
                        print(f"尝试打开计算器，命令: {' '.join(cmd)}")
                        if cmd[0] == 'start':
                            # 使用shell=True来执行start命令
                            self.calculator_process = subprocess.Popen(cmd, shell=True)
                            self.calculator_type = 'UWP'
                        elif cmd[0] == 'powershell':
                            # PowerShell命令
                            self.calculator_process = subprocess.Popen(cmd, shell=True)
                            self.calculator_type = 'UWP'
                        else:
                            # 普通可执行文件
                            self.calculator_process = subprocess.Popen(cmd)
                            self.calculator_type = 'Win32'

                        # 给进程一些时间启动
                        time.sleep(1)
                        self.is_calculator_open = True
                        self.last_command = cmd
                        print(f"✅ 计算器已打开 (类型: {self.calculator_type}, 命令: {' '.join(cmd)})")
                        return True
                    except FileNotFoundError:
                        print(f"命令未找到: {' '.join(cmd)}")
                        continue
                    except Exception as e:
                        print(f"尝试命令 {' '.join(cmd)} 失败: {e}")
                        continue

                print("所有计算器命令都失败了")
                return False
            except Exception as e:
                print(f"打开计算器失败: {e}")
                return False
        return False
    
    def close_calculator(self):
        """关闭计算器"""
        if not self.is_calculator_open:
            print("计算器未打开，无需关闭")
            return False

        print(f"尝试关闭计算器 (类型: {self.calculator_type})")

        success = False

        # 根据计算器类型选择关闭方法
        if self.calculator_type == 'UWP':
            success = self._close_uwp_calculator()
        elif self.calculator_type == 'Win32':
            success = self._close_win32_calculator()
        else:
            # 尝试所有方法
            success = self._close_uwp_calculator() or self._close_win32_calculator()

        if success:
            self.calculator_process = None
            self.is_calculator_open = False
            self.calculator_type = None
            self.last_command = None
            print("✅ 计算器已关闭")
            return True
        else:
            print("❌ 计算器关闭失败")
            return False

    def _close_uwp_calculator(self):
        """关闭UWP计算器"""
        try:
            # 基于实际测试的有效关闭命令
            close_commands = [
                # 最有效的方法：使用通配符匹配calc相关进程
                ['powershell', '-Command', 'Get-Process -Name "*calc*" | Stop-Process -Force'],
                # 针对具体的进程名称
                ['taskkill', '/F', '/IM', 'CalculatorApp.exe'],
                ['powershell', '-Command', 'Get-Process -Name "CalculatorApp" | Stop-Process -Force'],
                # 通过窗口标题关闭
                ['powershell', '-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -like "*计算器*"} | Stop-Process -Force'],
                ['powershell', '-Command', 'Get-Process | Where-Object {$_.MainWindowTitle -like "*Calculator*"} | Stop-Process -Force'],
                # 备用方法
                ['taskkill', '/F', '/IM', 'Calculator.exe'],
                ['taskkill', '/F', '/IM', 'calc.exe'],
                ['powershell', '-Command', 'Get-Process | Where-Object {$_.ProcessName -like "*calc*"} | Stop-Process -Force'],
            ]

            for cmd in close_commands:
                try:
                    print(f"尝试关闭命令: {' '.join(cmd)}")
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        print(f"✅ UWP计算器关闭成功 (命令: {' '.join(cmd)})")
                        return True
                    else:
                        print(f"命令执行失败: {result.stderr}")
                except subprocess.TimeoutExpired:
                    print(f"命令超时: {' '.join(cmd)}")
                except Exception as e:
                    print(f"命令执行异常: {e}")

            return False
        except Exception as e:
            print(f"关闭UWP计算器失败: {e}")
            return False

    def _close_win32_calculator(self):
        """关闭Win32计算器"""
        try:
            if self.calculator_process:
                self.calculator_process.terminate()
                self.calculator_process.wait(timeout=3)
                print("✅ Win32计算器进程已终止")
                return True
        except subprocess.TimeoutExpired:
            try:
                self.calculator_process.kill()
                print("✅ Win32计算器进程已强制终止")
                return True
            except Exception as e:
                print(f"强制终止进程失败: {e}")
        except Exception as e:
            print(f"关闭Win32计算器失败: {e}")

        return False
    
    def get_status(self):
        """获取计算器状态"""
        return "已打开" if self.is_calculator_open else "已关闭"


class CameraThread(QThread):
    """摄像头处理线程"""
    
    frame_ready = pyqtSignal(np.ndarray, str, str, float)
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.cap = None
        self.gesture_recognizer = GestureRecognizer()
        self.calculator_controller = CalculatorController()
        
        # 手势触发控制
        self.last_gesture = ""
        self.gesture_trigger_time = 0
        self.trigger_delay = 2.0  # 2秒防抖延迟
        
    def start_camera(self):
        """启动摄像头"""
        self.cap = cv2.VideoCapture(0)
        if not self.cap.isOpened():
            print("无法打开摄像头")
            return False
            
        # 设置摄像头参数
        self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        self.cap.set(cv2.CAP_PROP_FPS, 30)
        
        self.running = True
        return True
    
    def stop_camera(self):
        """停止摄像头"""
        self.running = False
        if self.cap:
            self.cap.release()
            
    def run(self):
        """线程主循环"""
        while self.running:
            if self.cap and self.cap.isOpened():
                ret, frame = self.cap.read()
                if ret:
                    # 水平翻转图像
                    frame = cv2.flip(frame, 1)
                    
                    # 处理手势识别
                    processed_frame, gesture, confidence = self._process_frame(frame)
                    
                    # 控制计算器
                    self._handle_gesture_control(gesture)
                    
                    # 发送信号
                    calculator_status = self.calculator_controller.get_status()
                    self.frame_ready.emit(processed_frame, gesture, calculator_status, confidence)
                    
            self.msleep(33)  # 约30fps
    
    def _process_frame(self, frame):
        """处理单帧图像"""
        # 转换颜色空间
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 手部检测
        results = self.gesture_recognizer.hands.process(rgb_frame)
        
        # 绘制手部关键点和骨架
        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # 绘制关键点
                self.gesture_recognizer.mp_drawing.draw_landmarks(
                    frame, hand_landmarks,
                    self.gesture_recognizer.mp_hands.HAND_CONNECTIONS,
                    self.gesture_recognizer.mp_drawing_styles.get_default_hand_landmarks_style(),
                    self.gesture_recognizer.mp_drawing_styles.get_default_hand_connections_style()
                )
                
                # 手势识别
                gesture, confidence = self.gesture_recognizer.detect_gesture(hand_landmarks)
                return frame, gesture, confidence
        
        return frame, "未检测到", 0.0
    
    def _handle_gesture_control(self, gesture):
        """处理手势控制逻辑"""
        current_time = time.time()

        # 检查是否需要触发动作
        if gesture != self.last_gesture:
            self.last_gesture = gesture
            self.gesture_trigger_time = current_time
            print(f"🔄 手势变化: {gesture}, 开始计时...")
        elif current_time - self.gesture_trigger_time >= self.trigger_delay:
            # 手势稳定超过延迟时间，执行相应动作
            print(f"⏰ 手势稳定 {self.trigger_delay} 秒: {gesture}")
            if gesture == "握拳":
                print("🤜 检测到握拳手势，尝试打开计算器...")
                result = self.calculator_controller.open_calculator()
                if result:
                    print("✅ 计算器打开操作完成")
                else:
                    print("❌ 计算器打开操作失败")
            elif gesture == "张开手掌":
                print("✋ 检测到张开手掌手势，尝试关闭计算器...")
                result = self.calculator_controller.close_calculator()
                if result:
                    print("✅ 计算器关闭操作完成")
                else:
                    print("❌ 计算器关闭操作失败")

            # 重置触发时间，避免重复触发
            self.gesture_trigger_time = current_time + self.trigger_delay


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.init_camera()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("实时手势控制计算器")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("实时手势控制计算器")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        main_layout.addWidget(title_label)
        
        # 摄像头显示区域
        self.video_label = QLabel()
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("border: 2px solid black")
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("摄像头未启动")
        main_layout.addWidget(self.video_label)
        
        # 状态信息区域
        status_layout = QHBoxLayout()
        
        # 手势状态
        self.gesture_label = QLabel("当前手势: 未检测到")
        self.gesture_label.setFont(QFont("Arial", 12))
        status_layout.addWidget(self.gesture_label)
        
        # 计算器状态
        self.calculator_label = QLabel("计算器状态: 已关闭")
        self.calculator_label.setFont(QFont("Arial", 12))
        status_layout.addWidget(self.calculator_label)
        
        # 置信度
        self.confidence_label = QLabel("置信度: 0.0")
        self.confidence_label.setFont(QFont("Arial", 12))
        status_layout.addWidget(self.confidence_label)
        
        main_layout.addLayout(status_layout)
        
        # 控制按钮
        button_layout = QHBoxLayout()
        
        self.start_button = QPushButton("启动摄像头")
        self.start_button.clicked.connect(self.start_camera)
        button_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止摄像头")
        self.stop_button.clicked.connect(self.stop_camera)
        self.stop_button.setEnabled(False)
        button_layout.addWidget(self.stop_button)
        
        self.exit_button = QPushButton("退出程序")
        self.exit_button.clicked.connect(self.close)
        button_layout.addWidget(self.exit_button)
        
        main_layout.addLayout(button_layout)
        
        # 说明文字
        instruction_label = QLabel(
            "使用说明:\n"
            "• 握拳手势: 打开系统计算器\n"
            "• 张开手掌: 关闭计算器\n"
            "• 按 'q' 键或点击退出按钮关闭程序"
        )
        instruction_label.setFont(QFont("Arial", 10))
        instruction_label.setStyleSheet("color: gray; margin: 10px;")
        main_layout.addWidget(instruction_label)
        
    def init_camera(self):
        """初始化摄像头线程"""
        self.camera_thread = CameraThread()
        self.camera_thread.frame_ready.connect(self.update_frame)
        
    def start_camera(self):
        """启动摄像头"""
        if self.camera_thread.start_camera():
            self.camera_thread.start()
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            print("摄像头已启动")
        else:
            self.video_label.setText("摄像头启动失败")
            
    def stop_camera(self):
        """停止摄像头"""
        self.camera_thread.stop_camera()
        self.camera_thread.wait()
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.video_label.setText("摄像头已停止")
        print("摄像头已停止")
        
    def update_frame(self, frame, gesture, calculator_status, confidence):
        """更新显示帧"""
        # 转换OpenCV图像为Qt图像
        height, width, _ = frame.shape
        bytes_per_line = 3 * width
        q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
        
        # 显示图像
        pixmap = QPixmap.fromImage(q_image)
        scaled_pixmap = pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.video_label.setPixmap(scaled_pixmap)
        
        # 更新状态标签
        self.gesture_label.setText(f"当前手势: {gesture}")
        self.calculator_label.setText(f"计算器状态: {calculator_status}")
        self.confidence_label.setText(f"置信度: {confidence:.2f}")
        
    def keyPressEvent(self, event):
        """键盘事件处理"""
        if event.key() == Qt.Key_Q:
            self.close()
            
    def closeEvent(self, event):
        """关闭事件处理"""
        if hasattr(self, 'camera_thread'):
            self.camera_thread.stop_camera()
            self.camera_thread.wait()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
